@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --paper: #F2EFEA;
  --ink: #111111;
  --accent: #96B59F; /* sampled from the original site */
}

html, body {
  height: 100%;
  background: var(--paper);
  color: var(--ink);
}
/* ===== overlay kill switch ===== */
body::before,
body::after { content: none !important; display: none !important; }

/* make sure main content sits above any stray fixed layers */
header, main, footer {
  position: relative;
  z-index: 2;
  isolation: isolate; /* creates a new stacking context */
}
/* Prevent accidental blends/filters/opacity inside header strip */
.no-wash, .no-wash * {
  opacity: 1 !important;
  filter: none !important;
  mix-blend-mode: normal !important;
  background-color: transparent !important;
}


/* Cursor */
.cursor-dot, .cursor-ring {
  position: fixed;
  top: 0; left: 0;
  pointer-events: none;
  z-index: 50;
  transform: translate(-50%, -50%);
}
.cursor-dot { width: 6px; height: 6px; border-radius: 9999px; background: var(--ink); }
.cursor-ring { width: 36px; height: 36px; border-radius: 9999px; border: 1px solid var(--ink); }

/* Doodle hover (stroke-dash) like the site */
.doodle-stroke path {
  fill: none;
  stroke: var(--accent);
  stroke-miterlimit: 10;
  stroke-dasharray: 1100;
  stroke-dashoffset: 1100;
  transition: stroke-dashoffset 600ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.doodle-wrap:hover .doodle-stroke path {
  stroke-dashoffset: 0;
}

/* Big editorial headings (split letters feel) */
.h-split span {
  display: inline-block;
  will-change: transform, opacity;
}

/* Horizontal "drag to explore" helper */
.helper-tag {
  @apply uppercase text-xs tracking-wider text-ink/70;
}
/* Display font utility */
.font-display { font-family: var(--font-fraunces), var(--font-newsreader), ui-serif, Georgia, serif; }

/* Drop cap */
.dropcap:first-letter{
  float:left; font-family:var(--font-fraunces), ui-serif, Georgia, serif;
  font-size:clamp(2.6rem,6vw,5rem); line-height:.85; padding-right:.35rem; color:var(--ink);
}

/* Vintage card + dotted */
.dotted{ border:1px dotted color-mix(in oklab, var(--ink) 55%, transparent); border-radius:12px; }
.boxed{ background:rgba(255,255,255,.5); border:1px solid rgba(17,17,17,.08); border-radius:14px; box-shadow:0 12px 30px rgba(0,0,0,.06); }

/* Stamp scallop */
.stamp{
  --bg: var(--paper);
  background:
    radial-gradient(circle at 10px 10px, transparent 8px, var(--bg) 8px) 0 0/20px 20px,
    linear-gradient(var(--bg), var(--bg));
  border:1px solid color-mix(in oklab, var(--ink) 30%, transparent);
  filter: drop-shadow(0 6px 12px rgba(0,0,0,.06));
  border-radius:6px;
}

/* Tiny top bar */
.topbar{ font-size:11px; letter-spacing:.06em; text-transform:uppercase; color:rgb(30 30 30 / .75); }

/* Simple marquee */
.marquee{ display:flex; overflow:hidden; user-select:none; }
.marquee-track{ display:flex; gap:16px; animation:marquee 30s linear infinite; will-change:transform; }
@keyframes marquee{ from{transform:translateX(0)} to{transform:translateX(-50%)} }
/* Hamburger */
.hamburger{ width:28px; height:22px; display:inline-flex; flex-direction:column; justify-content:space-between }
.hamburger span{ height:1px; width:100%; background:var(--ink); display:block; transition:transform .25s ease, width .25s ease }
.hamburger:hover span:nth-child(2){ width:80% }
.hamburger:hover span:nth-child(1){ transform:translateX(2px) }
.hamburger:hover span:nth-child(3){ transform:translateX(-2px) }

/* NEW badge (orange like the reference) */
.badge-new{
  background:#E38A3B; color:white; border-radius:4px;
  padding:.125rem .35rem; font-size:.625rem; letter-spacing:.06em; line-height:1; font-weight:700;
}

/* Topbar cosmetics (already added earlier but repeated for focus) */
.topbar{ font-size:11px; letter-spacing:.06em; text-transform:uppercase; }
