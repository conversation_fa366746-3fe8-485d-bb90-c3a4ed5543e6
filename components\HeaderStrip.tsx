'use client'
import Image from 'next/image'
import { useEffect, useRef } from 'react'
import { gsap } from '@/lib/gsap'

function NewBadge() {
  return <span className="badge-new">NEW</span>
}

function MiniEditorialCard({
  title, image, children,
}: { title: string; image: string; children: React.ReactNode }) {
  return (
    <article className="px-4 md:px-6 py-8">
      <div className="relative aspect-[16/9] rounded-md overflow-hidden mb-4">
        <Image src={image} alt={title} fill className="object-cover" />
      </div>
      <h4 className="font-display text-[22px] leading-none tracking-[.02em] uppercase flex items-center gap-2">
        {title} <NewBadge />
      </h4>
      <p className="mt-2 text-[15px] leading-6 text-ink/80">{children}</p>
    </article>
  )
}

function CenterCopy() {
  return (
    <div className="px-4 md:px-6 py-8 text-center md:text-left">
      <div className="font-display text-5xl md:text-[56px] leading-[0.95]">ALL WORK!</div>
      <div className="font-display text-[26px] md:text-[30px] leading-[1.05] mt-3">
        A Featured selection<br/> the latest work —<br/> of the last years.
      </div>
      <div className="mt-4 text-[12px] uppercase tracking-wider text-ink/70">
        <strong>Tip!</strong> Drag sideways to navigate
      </div>
    </div>
  )
}

export default function HeaderStrip() {
  const ref = useRef<HTMLElement>(null)
  useEffect(() => {
    if (!ref.current) return
    gsap.from(ref.current.querySelectorAll('[data-reveal] > *'), {
      opacity: 0, y: 14, stagger: 0.06, duration: 0.5, ease: 'power2.out'
    })
  }, [])

  return (
    <section ref={ref} className="no-wash relative z-[3] border-y border-ink/15">
      <div
        className="mx-auto max-w-6xl grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-ink/20"
      >
        <div data-reveal>
          <MiniEditorialCard
            title="AvroKO"
            image="/hero/thumbnail-small.jpeg"
          >
            AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture
            for hospitality, restaurant and bars.
          </MiniEditorialCard>
        </div>

        <div className="bg-paper/60" data-reveal>
          <CenterCopy />
        </div>

        <div data-reveal>
          <MiniEditorialCard
            title="The Roger Hub"
            image="/hero/thumbnail-small-3.jpeg"
          >
            The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration
            born out of a partnership with the legendary Roger Federer.
          </MiniEditorialCard>
        </div>
      </div>
    </section>
  )
}
